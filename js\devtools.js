const CD = chrome.devtools;
// Chrome DevTools Extension中不能使用console.log
const log = (...params) => CD.inspectedWindow.eval(`console.log(...${JSON.stringify(params)})`);

log('devtools.js log........');
chrome.devtools.network.onRequestFinished.addListener(
    async (...args) => {
        try {
            const [{
                // 请求的类型，查询参数，以及url
                request: { method, queryString, url },
                response: { bodySize, status },

                // 该方法可用于获取响应体
                getContent,
            }] = args;

            // log(method, queryString, url);
            // log(bodySize, status);
            // log("url+++++++++++" + url)
            if (url === 'https://sso.tool.bestpay.net/get_user') {
                // 将callback转为await promise
                // warn: content在getContent回调函数中，而不是getContent的返回值
                const content = await new Promise((res, rej) => getContent(res));
                log('response=+++++++   ' + content);
                log(content)
                log("sso_session_id新的是" + JSON.parse(content).data.sso_session_id)
                var sessionId = JSON.parse(content).data.sso_session_id
                chrome.runtime.sendMessage({
                    action: "SEND_SSO_ID",
                    sso_session_id: sessionId
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        log("消息发送失败:", chrome.runtime.lastError.message);
                    } else {
                        log("后台返回结果:", response);
                    }
                });
                // await sendSsoId(JSON.parse(content).data.sso_session_id)
            }
            // 调用background.js中的函数
            // var bg = chrome.extension.getBackgroundPage();
            // bg.sendRequest(url);

        } catch (err) {
            log(err.stack || err.toString());
        }
    });
