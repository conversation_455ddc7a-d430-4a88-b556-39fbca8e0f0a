

// document.scripts[0].src = "axios.min.js"
// function addScript(url) {
//     var script = document.createElement('script');
//     script.setAttribute('type', 'text/javascript');
//     script.setAttribute('src', url);
//     document.getElementsByTagName('head')[0].appendChild(script);
// }
// importScripts('axios.min.js');
function sendRequest(args) {
    console.log("bg sendRequest");
    this.axios({
        method: args.method,
        url: args.url,
        data: args.data,
        headers: args.headers
    }).then(resp => {  //请求成功
        console.log("sendRequest:" + resp);
    }).catch(error => { //请求失败
        console.log(error);
    }).then(() => { //() => {} 相当于 function(){}
        console.log("不管怎么样都要执行");  //必须执行的代码，相当于finnally
    })
}

function sendSsoId(args) {
    axios({
        method: 'POST',
        url: "https://quality.test.bestpay.net/bestpay/ssotoken",
        data: { ssotoken: args }
    }).then((res) => {
        console.log("发送ssoid成功")
        alert("发送成功")
        return res;
    })
}
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'SEND_SSO_ID') {
        const ssoId = request.sso_session_id;
        console.log("发送的ssoid是" + ssoId)
        const url = 'https://quality.test.bestpay.net/bestpay/ssotoken';
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ ssotoken: ssoId })
        }).then(res => {
            console.log('发送 SSO ID 成功:', res.data);
            sendResponse({ success: true, data: res.data });
        }).catch(err => {
            console.error('发送失败:', err);
            sendResponse({ success: false, error: err.message });
        })
        return true; // 表示需要异步回复
    }
});

// // export default sendSsoId;