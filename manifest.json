{
  "name": "chromepulgin",
  "version": "1.0.1",
  "minimum_chrome_version": "100.0",
  "description": "chrome devtools",
  "manifest_version": 3,
  // devtools页面入口，注意只能指向一个HTML文件，不能是JS文件
  "devtools_page": "devtools.html",
  "content_scripts": [
    {
      "matches": ["*://*/*"],
      "css": [],
      "js": ["scripts/axios.min.js", "scripts/vue.min.js"]
    }
  ],
  "background": {
    "service_worker": "scripts/background.js",
    "persistent": false //定义了常驻后台的方式——当其值为true时，表示扩展将一直在后台运行，无论其是否正在工作
  },
  "permissions": [
    "webRequest",
    "storage",
    "tabs",
    "http://*/*",
    "https://*/*",
    "contextMenus",
    "chrome://favicon/"
  ],
  "icons": {}
}
